'use client'

import { useVideoThumbnail } from '@/hooks/useVideoThumbnail'

interface VideoThumbnailProps {
  videoUrl: string
  alt?: string
  className?: string
  aspectRatio?: number
  timeInSeconds?: number
  showPlayButton?: boolean
  playButtonSize?: 'sm' | 'md' | 'lg'
}

export function VideoThumbnail({
  videoUrl,
  alt = 'Video thumbnail',
  className = '',
  aspectRatio = 16/10,
  timeInSeconds = 1,
  showPlayButton = true,
  playButtonSize = 'md'
}: VideoThumbnailProps) {
  const { thumbnail, loading, error } = useVideoThumbnail(videoUrl, {
    aspectRatio,
    timeInSeconds,
    enabled: true
  })

  const playButtonSizes = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16'
  }

  const playIconSizes = {
    sm: 'w-3 h-3',
    md: 'w-5 h-5',
    lg: 'w-7 h-7'
  }

  if (loading) {
    return (
      <div className={`relative bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center ${className}`}>
        <div className="animate-pulse">
          <div className="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
              <path d="M6.3 2.841A1.5 1.5 0 004 4.11V15.89a1.5 1.5 0 002.3 1.269l9.344-5.89a1.5 1.5 0 000-2.538L6.3 2.84z"/>
            </svg>
          </div>
        </div>
        <div className="absolute top-2 left-2">
          <span className="bg-red-500/90 text-white text-xs px-1.5 py-0.5 rounded font-medium">
            VIDEO
          </span>
        </div>
      </div>
    )
  }

  if (error || !thumbnail) {
    // Fallback: Use video element with poster frame
    return (
      <div className={`relative bg-black ${className}`}>
        <video
          className="w-full h-full object-cover"
          preload="metadata"
          muted
          poster={videoUrl + '#t=1'}
        >
          <source src={videoUrl} type="video/mp4" />
        </video>
        {showPlayButton && (
          <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
            <div className={`${playButtonSizes[playButtonSize]} bg-white/95 rounded-full flex items-center justify-center shadow-lg hover:bg-white transition-colors`}>
              <svg className={`${playIconSizes[playButtonSize]} text-gray-800 ml-0.5`} fill="currentColor" viewBox="0 0 20 20">
                <path d="M6.3 2.841A1.5 1.5 0 004 4.11V15.89a1.5 1.5 0 002.3 1.269l9.344-5.89a1.5 1.5 0 000-2.538L6.3 2.84z"/>
              </svg>
            </div>
          </div>
        )}
        <div className="absolute top-2 left-2">
          <span className="bg-red-500/90 text-white text-xs px-1.5 py-0.5 rounded font-medium">
            VIDEO
          </span>
        </div>
      </div>
    )
  }

  return (
    <div className={`relative ${className}`}>
      <img
        src={thumbnail}
        alt={alt}
        className="w-full h-full object-cover"
      />
      {showPlayButton && (
        <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
          <div className={`${playButtonSizes[playButtonSize]} bg-white/95 rounded-full flex items-center justify-center shadow-lg hover:bg-white transition-colors`}>
            <svg className={`${playIconSizes[playButtonSize]} text-gray-800 ml-0.5`} fill="currentColor" viewBox="0 0 20 20">
              <path d="M6.3 2.841A1.5 1.5 0 004 4.11V15.89a1.5 1.5 0 002.3 1.269l9.344-5.89a1.5 1.5 0 000-2.538L6.3 2.84z"/>
            </svg>
          </div>
        </div>
      )}
      <div className="absolute top-2 left-2">
        <span className="bg-red-500/90 text-white text-xs px-1.5 py-0.5 rounded font-medium">
          VIDEO
        </span>
      </div>
    </div>
  )
}

/**
 * Generate a thumbnail image from a video URL
 * @param videoUrl - The URL of the video
 * @param timeInSeconds - Time in seconds to capture the thumbnail (default: 1)
 * @returns Promise<string> - Data URL of the generated thumbnail
 */
export function generateVideoThumbnail(
  videoUrl: string, 
  timeInSeconds: number = 1
): Promise<string> {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video')
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    
    if (!ctx) {
      reject(new Error('Canvas context not available'))
      return
    }

    video.crossOrigin = 'anonymous'
    video.preload = 'metadata'
    video.muted = true // Required for some browsers
    
    video.onloadedmetadata = () => {
      // Set canvas dimensions to match video
      canvas.width = video.videoWidth
      canvas.height = video.videoHeight
      
      // Seek to the desired time
      video.currentTime = Math.min(timeInSeconds, video.duration - 0.1)
    }
    
    video.onseeked = () => {
      try {
        // Draw the current frame to canvas
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height)
        
        // Convert canvas to data URL
        const thumbnailDataUrl = canvas.toDataURL('image/jpeg', 0.8)
        
        // Clean up
        video.remove()
        canvas.remove()
        
        resolve(thumbnailDataUrl)
      } catch (error) {
        reject(error)
      }
    }
    
    video.onerror = () => {
      reject(new Error('Failed to load video for thumbnail generation'))
    }
    
    video.onloadstart = () => {
      console.log('Loading video for thumbnail:', videoUrl)
    }
    
    // Start loading the video
    video.src = videoUrl
    video.load()
  })
}

/**
 * Generate a thumbnail with specific aspect ratio
 * @param videoUrl - The URL of the video
 * @param aspectRatio - Desired aspect ratio (width/height)
 * @param timeInSeconds - Time in seconds to capture the thumbnail
 * @returns Promise<string> - Data URL of the generated thumbnail
 */
export function generateVideoThumbnailWithAspectRatio(
  videoUrl: string,
  aspectRatio: number = 16/10, // Default to match image cards
  timeInSeconds: number = 1
): Promise<string> {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video')
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    
    if (!ctx) {
      reject(new Error('Canvas context not available'))
      return
    }

    video.crossOrigin = 'anonymous'
    video.preload = 'metadata'
    video.muted = true
    
    video.onloadedmetadata = () => {
      const videoAspectRatio = video.videoWidth / video.videoHeight
      
      // Calculate canvas dimensions to match desired aspect ratio
      let canvasWidth, canvasHeight
      let sourceX = 0, sourceY = 0, sourceWidth = video.videoWidth, sourceHeight = video.videoHeight
      
      if (videoAspectRatio > aspectRatio) {
        // Video is wider than desired ratio - crop sides
        canvasHeight = 400 // Fixed height for consistency
        canvasWidth = canvasHeight * aspectRatio
        sourceWidth = video.videoHeight * aspectRatio
        sourceX = (video.videoWidth - sourceWidth) / 2
      } else {
        // Video is taller than desired ratio - crop top/bottom
        canvasWidth = 400 * aspectRatio
        canvasHeight = 400
        sourceHeight = video.videoWidth / aspectRatio
        sourceY = (video.videoHeight - sourceHeight) / 2
      }
      
      canvas.width = canvasWidth
      canvas.height = canvasHeight
      
      // Seek to the desired time
      video.currentTime = Math.min(timeInSeconds, video.duration - 0.1)
    }
    
    video.onseeked = () => {
      try {
        // Draw the cropped frame to canvas
        const videoAspectRatio = video.videoWidth / video.videoHeight
        let sourceX = 0, sourceY = 0, sourceWidth = video.videoWidth, sourceHeight = video.videoHeight
        
        if (videoAspectRatio > aspectRatio) {
          // Video is wider - crop sides
          sourceWidth = video.videoHeight * aspectRatio
          sourceX = (video.videoWidth - sourceWidth) / 2
        } else {
          // Video is taller - crop top/bottom
          sourceHeight = video.videoWidth / aspectRatio
          sourceY = (video.videoHeight - sourceHeight) / 2
        }
        
        ctx.drawImage(
          video,
          sourceX, sourceY, sourceWidth, sourceHeight,
          0, 0, canvas.width, canvas.height
        )
        
        // Convert canvas to data URL
        const thumbnailDataUrl = canvas.toDataURL('image/jpeg', 0.8)
        
        // Clean up
        video.remove()
        canvas.remove()
        
        resolve(thumbnailDataUrl)
      } catch (error) {
        reject(error)
      }
    }
    
    video.onerror = () => {
      reject(new Error('Failed to load video for thumbnail generation'))
    }
    
    // Start loading the video
    video.src = videoUrl
    video.load()
  })
}
